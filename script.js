/**
 * NEXUS STUDIOS - INTERACTIVE JAVASCRIPT ENGINE
 * Where AI meets creative brilliance
 * 
 * Features:
 * - Particle animation system with mouse interaction
 * - Scroll-triggered animations with Intersection Observer
 * - Magnetic button effects
 * - Typewriter text animations
 * - Smooth scrolling and parallax effects
 * - Dark mode toggle
 * - Form validation and submission
 * - Carousel functionality
 * - Performance optimizations
 */

class NexusStudio {
  constructor() {
    this.init();
    this.setupEventListeners();
    this.initializeComponents();
  }

  init() {
    // Performance optimization
    this.rafId = null;
    this.isScrolling = false;
    this.scrollTimeout = null;
    
    // Mouse tracking
    this.mouse = { x: 0, y: 0 };
    this.targetMouse = { x: 0, y: 0 };
    
    // Particle system
    this.particles = [];
    this.particleCount = 50;
    
    // Constellation system
    this.stars = [];
    this.starCount = 100;
    this.connections = [];
    
    // Audio context for sound effects
    this.audioContext = null;
    this.initAudio();
    
    // Theme state
    this.theme = localStorage.getItem('theme') || 'dark';
    this.applyTheme();
  }

  setupEventListeners() {
    // Mouse movement tracking
    document.addEventListener('mousemove', this.handleMouseMove.bind(this));
    
    // Scroll events
    window.addEventListener('scroll', this.handleScroll.bind(this));
    
    // Resize events
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // Theme toggle
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      themeToggle.addEventListener('click', this.toggleTheme.bind(this));
    }
    
    // Navigation toggle
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    if (navToggle && navMenu) {
      navToggle.addEventListener('click', () => {
        const isExpanded = navToggle.getAttribute('aria-expanded') === 'true';
        navToggle.setAttribute('aria-expanded', !isExpanded);
        navMenu.classList.toggle('active');
      });
    }
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(link => {
      link.addEventListener('click', this.handleSmoothScroll.bind(this));
    });
    
    // Loading screen
    window.addEventListener('load', this.hideLoadingScreen.bind(this));
  }

  initializeComponents() {
    this.initParticleSystem();
    this.initConstellation();
    this.initScrollAnimations();
    this.initMagneticButtons();
    this.initTypewriter();
    this.initPortfolioFilters();
    this.initTestimonialCarousel();
    this.initContactForm();
    this.initNewsletterForm();
    this.init3DTilt();
  }

  // ===================================
  // AUDIO SYSTEM
  // ===================================

  initAudio() {
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
    } catch (e) {
      console.log('Web Audio API not supported');
    }
  }

  playHoverSound(frequency = 800, duration = 100) {
    if (!this.audioContext) return;
    
    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);
    
    oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
    oscillator.type = 'sine';
    
    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.1, this.audioContext.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration / 1000);
    
    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + duration / 1000);
  }

  // ===================================
  // PARTICLE SYSTEM
  // ===================================

  initParticleSystem() {
    this.canvas = document.getElementById('particle-canvas');
    if (!this.canvas) return;
    
    this.ctx = this.canvas.getContext('2d');
    this.resizeCanvas();
    
    // Create particles
    for (let i = 0; i < this.particleCount; i++) {
      this.particles.push(this.createParticle());
    }
    
    this.animateParticles();
  }

  createParticle() {
    return {
      x: Math.random() * this.canvas.width,
      y: Math.random() * this.canvas.height,
      vx: (Math.random() - 0.5) * 0.5,
      vy: (Math.random() - 0.5) * 0.5,
      size: Math.random() * 3 + 1,
      opacity: Math.random() * 0.5 + 0.2,
      color: this.getRandomColor(),
      angle: Math.random() * Math.PI * 2,
      rotationSpeed: (Math.random() - 0.5) * 0.02
    };
  }

  getRandomColor() {
    const colors = ['#6366f1', '#8b5cf6', '#ec4899', '#06b6d4', '#10b981'];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  animateParticles() {
    if (!this.ctx) return;
    
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    this.particles.forEach(particle => {
      // Mouse interaction
      const dx = this.mouse.x - particle.x;
      const dy = this.mouse.y - particle.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      if (distance < 100) {
        const force = (100 - distance) / 100;
        particle.vx += dx * force * 0.001;
        particle.vy += dy * force * 0.001;
      }
      
      // Update position
      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.angle += particle.rotationSpeed;
      
      // Boundary check
      if (particle.x < 0 || particle.x > this.canvas.width) particle.vx *= -1;
      if (particle.y < 0 || particle.y > this.canvas.height) particle.vy *= -1;
      
      // Keep particles in bounds
      particle.x = Math.max(0, Math.min(this.canvas.width, particle.x));
      particle.y = Math.max(0, Math.min(this.canvas.height, particle.y));
      
      // Draw particle
      this.ctx.save();
      this.ctx.translate(particle.x, particle.y);
      this.ctx.rotate(particle.angle);
      this.ctx.globalAlpha = particle.opacity;
      this.ctx.fillStyle = particle.color;
      this.ctx.fillRect(-particle.size / 2, -particle.size / 2, particle.size, particle.size);
      this.ctx.restore();
    });
    
    this.rafId = requestAnimationFrame(this.animateParticles.bind(this));
  }

  // ===================================
  // CONSTELLATION BACKGROUND
  // ===================================

  initConstellation() {
    this.constellationEl = document.getElementById('constellation');
    if (!this.constellationEl) return;
    
    // Create stars
    for (let i = 0; i < this.starCount; i++) {
      this.stars.push(this.createStar());
    }
    
    this.renderConstellation();
    this.animateConstellation();
  }

  createStar() {
    return {
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      size: Math.random() * 2 + 1,
      opacity: Math.random() * 0.8 + 0.2,
      twinkleSpeed: Math.random() * 0.02 + 0.01
    };
  }

  renderConstellation() {
    this.constellationEl.innerHTML = '';
    
    this.stars.forEach((star, index) => {
      const starEl = document.createElement('div');
      starEl.style.position = 'absolute';
      starEl.style.left = star.x + 'px';
      starEl.style.top = star.y + 'px';
      starEl.style.width = star.size + 'px';
      starEl.style.height = star.size + 'px';
      starEl.style.backgroundColor = '#ffffff';
      starEl.style.borderRadius = '50%';
      starEl.style.opacity = star.opacity;
      starEl.style.pointerEvents = 'none';
      starEl.dataset.index = index;
      
      this.constellationEl.appendChild(starEl);
    });
  }

  animateConstellation() {
    this.stars.forEach((star, index) => {
      star.opacity += Math.sin(Date.now() * star.twinkleSpeed) * 0.1;
      star.opacity = Math.max(0.1, Math.min(0.9, star.opacity));
      
      const starEl = this.constellationEl.querySelector(`[data-index="${index}"]`);
      if (starEl) {
        starEl.style.opacity = star.opacity;
      }
    });
    
    requestAnimationFrame(this.animateConstellation.bind(this));
  }

  // ===================================
  // SCROLL ANIMATIONS
  // ===================================

  initScrollAnimations() {
    // Intersection Observer for scroll-triggered animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };
    
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
          
          // Trigger specific animations based on element type
          if (entry.target.classList.contains('service-card')) {
            this.animateServiceCard(entry.target);
          } else if (entry.target.classList.contains('portfolio-item')) {
            this.animatePortfolioItem(entry.target);
          }
        }
      });
    }, observerOptions);
    
    // Observe elements
    document.querySelectorAll('.service-card, .portfolio-item, .team-member, .testimonial-slide').forEach(el => {
      this.observer.observe(el);
    });
    
    // Parallax scrolling
    this.initParallax();
  }

  initParallax() {
    const parallaxElements = document.querySelectorAll('.floating-element');
    
    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset;
      
      parallaxElements.forEach(el => {
        const speed = el.dataset.speed || 0.5;
        const yPos = -(scrolled * speed);
        el.style.transform = `translateY(${yPos}px)`;
      });
    });
  }

  animateServiceCard(card) {
    const icon = card.querySelector('.card-icon');
    if (icon) {
      setTimeout(() => {
        icon.style.transform = 'scale(1.1) rotateY(360deg)';
        this.playHoverSound(600, 150);
      }, 200);
    }
  }

  animatePortfolioItem(item) {
    item.style.transform = 'translateY(0) scale(1)';
    item.style.opacity = '1';
  }

  // ===================================
  // MAGNETIC BUTTONS
  // ===================================

  initMagneticButtons() {
    const magneticBtns = document.querySelectorAll('.magnetic-btn');
    
    magneticBtns.forEach(btn => {
      btn.addEventListener('mouseenter', (e) => {
        this.playHoverSound(800, 100);
      });
      
      btn.addEventListener('mousemove', (e) => {
        const rect = btn.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;
        
        btn.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px)`;
      });
      
      btn.addEventListener('mouseleave', () => {
        btn.style.transform = 'translate(0, 0)';
      });
    });
  }

  // ===================================
  // TYPEWRITER EFFECT
  // ===================================

  initTypewriter() {
    const typewriterElements = document.querySelectorAll('.typewriter-text');
    
    typewriterElements.forEach(el => {
      const text = el.dataset.text;
      if (text) {
        this.typeWriter(el, text, 50);
      }
    });
  }

  typeWriter(element, text, speed) {
    let i = 0;
    element.innerHTML = '';
    element.style.width = '0';

    const timer = setInterval(() => {
      if (i < text.length) {
        element.innerHTML += text.charAt(i);
        element.style.width = ((i + 1) / text.length * 100) + '%';
        i++;
      } else {
        clearInterval(timer);
        // Remove cursor after typing is complete
        setTimeout(() => {
          element.style.borderRight = 'none';
        }, 1000);
      }
    }, speed);
  }

  // ===================================
  // PORTFOLIO FILTERS
  // ===================================

  initPortfolioFilters() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const portfolioItems = document.querySelectorAll('.portfolio-item');

    filterBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const filter = btn.dataset.filter;

        // Update active button
        filterBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');

        // Filter items
        portfolioItems.forEach(item => {
          const categories = item.dataset.category.split(' ');

          if (filter === 'all' || categories.includes(filter)) {
            item.style.display = 'block';
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';

            setTimeout(() => {
              item.style.opacity = '1';
              item.style.transform = 'translateY(0)';
            }, 100);
          } else {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';

            setTimeout(() => {
              item.style.display = 'none';
            }, 300);
          }
        });

        this.playHoverSound(700, 120);
      });
    });
  }

  // ===================================
  // TESTIMONIAL CAROUSEL
  // ===================================

  initTestimonialCarousel() {
    const slides = document.querySelectorAll('.testimonial-slide');
    const dots = document.querySelectorAll('.dot');
    const prevBtn = document.querySelector('.carousel-btn.prev');
    const nextBtn = document.querySelector('.carousel-btn.next');

    if (!slides.length) return;

    let currentSlide = 0;
    const totalSlides = slides.length;

    const showSlide = (index) => {
      slides.forEach((slide, i) => {
        slide.classList.toggle('active', i === index);
      });

      dots.forEach((dot, i) => {
        dot.classList.toggle('active', i === index);
      });
    };

    const nextSlide = () => {
      currentSlide = (currentSlide + 1) % totalSlides;
      showSlide(currentSlide);
      this.playHoverSound(900, 100);
    };

    const prevSlide = () => {
      currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
      showSlide(currentSlide);
      this.playHoverSound(900, 100);
    };

    // Event listeners
    if (nextBtn) nextBtn.addEventListener('click', nextSlide);
    if (prevBtn) prevBtn.addEventListener('click', prevSlide);

    dots.forEach((dot, index) => {
      dot.addEventListener('click', () => {
        currentSlide = index;
        showSlide(currentSlide);
        this.playHoverSound(900, 100);
      });
    });

    // Auto-play carousel
    setInterval(nextSlide, 5000);
  }

  // ===================================
  // CONTACT FORM
  // ===================================

  initContactForm() {
    const form = document.getElementById('contact-form');
    if (!form) return;

    const inputs = form.querySelectorAll('.form-input, .form-select, .form-textarea');
    const submitBtn = form.querySelector('.form-submit');
    const successMsg = form.querySelector('.form-success');

    // Real-time validation
    inputs.forEach(input => {
      input.addEventListener('blur', () => this.validateField(input));
      input.addEventListener('input', () => this.clearFieldError(input));
    });

    // Form submission
    form.addEventListener('submit', async (e) => {
      e.preventDefault();

      if (this.validateForm(form)) {
        await this.submitForm(form, submitBtn, successMsg);
      }
    });
  }

  validateField(field) {
    const value = field.value.trim();
    const errorEl = document.getElementById(field.name + '-error');
    let isValid = true;
    let errorMessage = '';

    // Required field validation
    if (field.hasAttribute('required') && !value) {
      isValid = false;
      errorMessage = 'This field is required';
    }

    // Email validation
    if (field.type === 'email' && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid email address';
      }
    }

    // Update UI
    if (errorEl) {
      errorEl.textContent = errorMessage;
      errorEl.classList.toggle('visible', !isValid);
    }

    field.classList.toggle('error', !isValid);

    return isValid;
  }

  clearFieldError(field) {
    const errorEl = document.getElementById(field.name + '-error');
    if (errorEl) {
      errorEl.classList.remove('visible');
    }
    field.classList.remove('error');
  }

  validateForm(form) {
    const inputs = form.querySelectorAll('.form-input, .form-select, .form-textarea');
    let isValid = true;

    inputs.forEach(input => {
      if (!this.validateField(input)) {
        isValid = false;
      }
    });

    return isValid;
  }

  async submitForm(form, submitBtn, successMsg) {
    // Show loading state
    submitBtn.classList.add('loading');
    submitBtn.disabled = true;

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Show success message
      successMsg.classList.add('visible');
      form.reset();

      // Play success sound
      this.playHoverSound(1000, 200);

      // Hide success message after 5 seconds
      setTimeout(() => {
        successMsg.classList.remove('visible');
      }, 5000);

    } catch (error) {
      console.error('Form submission error:', error);
      alert('There was an error submitting your message. Please try again.');
    } finally {
      // Reset button state
      submitBtn.classList.remove('loading');
      submitBtn.disabled = false;
    }
  }

  // ===================================
  // NEWSLETTER FORM
  // ===================================

  initNewsletterForm() {
    const form = document.getElementById('newsletter-form');
    if (!form) return;

    form.addEventListener('submit', async (e) => {
      e.preventDefault();

      const input = form.querySelector('.newsletter-input');
      const email = input.value.trim();

      if (email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        // Simulate subscription
        input.value = '';
        input.placeholder = 'Subscribed! ✓';
        this.playHoverSound(1200, 150);

        setTimeout(() => {
          input.placeholder = 'Enter your email';
        }, 3000);
      } else {
        input.style.borderColor = '#ef4444';
        setTimeout(() => {
          input.style.borderColor = '';
        }, 2000);
      }
    });
  }

  // ===================================
  // 3D TILT EFFECTS
  // ===================================

  init3DTilt() {
    const tiltElements = document.querySelectorAll('[data-tilt]');

    tiltElements.forEach(el => {
      el.addEventListener('mousemove', (e) => {
        const rect = el.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const centerX = rect.width / 2;
        const centerY = rect.height / 2;

        const rotateX = (y - centerY) / centerY * -10;
        const rotateY = (x - centerX) / centerX * 10;

        el.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.05, 1.05, 1.05)`;
      });

      el.addEventListener('mouseleave', () => {
        el.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)';
      });
    });
  }

  // ===================================
  // UTILITY FUNCTIONS
  // ===================================

  handleMouseMove(e) {
    this.targetMouse.x = e.clientX;
    this.targetMouse.y = e.clientY;

    // Smooth mouse tracking
    this.mouse.x += (this.targetMouse.x - this.mouse.x) * 0.1;
    this.mouse.y += (this.targetMouse.y - this.mouse.y) * 0.1;
  }

  handleScroll() {
    if (!this.isScrolling) {
      this.isScrolling = true;

      // Update navigation on scroll
      const nav = document.querySelector('.nav');
      if (nav) {
        nav.classList.toggle('scrolled', window.scrollY > 50);
      }

      // Dynamic gradient based on scroll
      this.updateScrollGradient();
    }

    clearTimeout(this.scrollTimeout);
    this.scrollTimeout = setTimeout(() => {
      this.isScrolling = false;
    }, 100);
  }

  updateScrollGradient() {
    const scrollPercent = window.scrollY / (document.body.scrollHeight - window.innerHeight);
    const hue = Math.floor(scrollPercent * 360);

    document.body.style.background = `linear-gradient(135deg,
      hsl(${hue}, 70%, 10%) 0%,
      hsl(${(hue + 60) % 360}, 60%, 15%) 50%,
      hsl(${(hue + 120) % 360}, 50%, 20%) 100%)`;
  }

  handleResize() {
    this.resizeCanvas();

    // Update constellation
    if (this.constellationEl) {
      this.stars.forEach(star => {
        star.x = Math.random() * window.innerWidth;
        star.y = Math.random() * window.innerHeight;
      });
      this.renderConstellation();
    }
  }

  resizeCanvas() {
    if (!this.canvas) return;

    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;
  }

  handleSmoothScroll(e) {
    e.preventDefault();

    const targetId = e.currentTarget.getAttribute('href');
    const targetElement = document.querySelector(targetId);

    if (targetElement) {
      const offsetTop = targetElement.offsetTop - 80; // Account for fixed nav

      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
  }

  toggleTheme() {
    this.theme = this.theme === 'dark' ? 'light' : 'dark';
    this.applyTheme();
    localStorage.setItem('theme', this.theme);
    this.playHoverSound(600, 100);
  }

  applyTheme() {
    document.documentElement.setAttribute('data-theme', this.theme);
  }

  hideLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
      setTimeout(() => {
        loadingScreen.classList.add('hidden');

        // Remove from DOM after transition
        setTimeout(() => {
          loadingScreen.remove();
        }, 500);
      }, 1000);
    }
  }

  // ===================================
  // CLEANUP
  // ===================================

  destroy() {
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
    }

    if (this.observer) {
      this.observer.disconnect();
    }

    if (this.audioContext) {
      this.audioContext.close();
    }
  }
}

// ===================================
// INITIALIZATION
// ===================================

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.nexusStudio = new NexusStudio();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (window.nexusStudio) {
    window.nexusStudio.destroy();
  }
});
