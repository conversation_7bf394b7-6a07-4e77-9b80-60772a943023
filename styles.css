/* ===================================
   NEXUS STUDIOS - ADVANCED CSS FRAMEWORK
   Where AI meets creative brilliance
   =================================== */

/* CSS Custom Properties (Design Tokens) */
:root {
  /* Color System */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  /* Gradient System */
  --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
  --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 50%, #8b5cf6 100%);
  --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #ef4444 50%, #ec4899 100%);
  --gradient-dark: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  
  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  --glass-backdrop: blur(20px);
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  /* Font Sizes (Fluid Typography) */
  --text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --text-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
  --text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem);
  --text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3.5rem);
  --text-5xl: clamp(3rem, 2.5rem + 2.5vw, 4.5rem);
  --text-6xl: clamp(3.75rem, 3rem + 3.75vw, 6rem);
  
  /* Spacing System */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
  
  /* Animation Timing */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Dark Mode Variables */
[data-theme="dark"] {
  --glass-bg: rgba(0, 0, 0, 0.2);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* ===================================
   RESET & BASE STYLES
   =================================== */

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: 1.6;
  color: #1f2937;
  background: #0f172a;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===================================
   UTILITY CLASSES
   =================================== */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-8);
  }
}

.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-bg {
  background: var(--gradient-primary);
}

/* Glass Card Component */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  box-shadow: var(--glass-shadow);
  transition: all 0.3s var(--ease-out);
}

.glass-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Magnetic Button Component */
.magnetic-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4) var(--space-8);
  border: none;
  border-radius: var(--radius-full);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: 600;
  text-decoration: none;
  color: white;
  background: transparent;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s var(--ease-out);
  z-index: 1;
}

.magnetic-btn .btn-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  border-radius: inherit;
  transition: all 0.3s var(--ease-out);
  z-index: -1;
}

.magnetic-btn .btn-text {
  position: relative;
  z-index: 2;
  transition: all 0.3s var(--ease-out);
}

.magnetic-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.4);
}

.magnetic-btn:hover .btn-bg {
  transform: scale(1.05);
  filter: brightness(1.1);
}

.magnetic-btn:active {
  transform: translateY(0);
}

/* ===================================
   LOADING SCREEN
   =================================== */

.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.5s var(--ease-out), visibility 0.5s var(--ease-out);
}

.loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--space-6);
}

.logo-svg {
  width: 100%;
  height: 100%;
}

.logo-path {
  stroke-dasharray: 300;
  stroke-dashoffset: 300;
  animation: drawLogo 2s var(--ease-out) forwards;
}

@keyframes drawLogo {
  to {
    stroke-dashoffset: 0;
  }
}

.loading-text {
  display: flex;
  justify-content: center;
  gap: var(--space-2);
  margin-bottom: var(--space-8);
}

.loading-letter {
  font-size: var(--text-2xl);
  font-weight: 700;
  opacity: 0;
  animation: fadeInUp 0.6s var(--ease-out) forwards;
}

.loading-letter:nth-child(1) { animation-delay: 0.1s; }
.loading-letter:nth-child(2) { animation-delay: 0.2s; }
.loading-letter:nth-child(3) { animation-delay: 0.3s; }
.loading-letter:nth-child(4) { animation-delay: 0.4s; }
.loading-letter:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loading-progress {
  width: 200px;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin: 0 auto;
}

.progress-bar {
  width: 0;
  height: 100%;
  background: var(--gradient-primary);
  border-radius: inherit;
  animation: loadProgress 2s var(--ease-out) forwards;
}

@keyframes loadProgress {
  to {
    width: 100%;
  }
}

/* ===================================
   PARTICLE CANVAS & CONSTELLATION
   =================================== */

.particle-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.constellation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
  opacity: 0.6;
}

/* ===================================
   NAVIGATION
   =================================== */

.nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: var(--space-4) 0;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
  z-index: var(--z-fixed);
  transition: all 0.3s var(--ease-out);
}

.nav.scrolled {
  padding: var(--space-3) 0;
  box-shadow: var(--glass-shadow);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  text-decoration: none;
  color: white;
  font-weight: 700;
  font-size: var(--text-lg);
  transition: all 0.3s var(--ease-out);
}

.nav-logo:hover {
  transform: scale(1.05);
}

.logo-icon {
  width: 32px;
  height: 32px;
  color: #6366f1;
}

.nav-menu {
  display: none;
  list-style: none;
  gap: var(--space-8);
}

@media (min-width: 768px) {
  .nav-menu {
    display: flex;
  }
}

.nav-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s var(--ease-out);
  position: relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: width 0.3s var(--ease-out);
}

.nav-link:hover {
  color: white;
}

.nav-link:hover::after {
  width: 100%;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-full);
  background: var(--glass-bg);
  color: white;
  cursor: pointer;
  transition: all 0.3s var(--ease-out);
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.theme-icon {
  font-size: var(--text-lg);
  transition: all 0.3s var(--ease-out);
}

.theme-icon-dark {
  display: none;
}

[data-theme="dark"] .theme-icon-light {
  display: none;
}

[data-theme="dark"] .theme-icon-dark {
  display: block;
}

.nav-cta {
  display: none;
}

@media (min-width: 768px) {
  .nav-cta {
    display: inline-flex;
  }
}

.nav-toggle {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  cursor: pointer;
  gap: 4px;
}

@media (min-width: 768px) {
  .nav-toggle {
    display: none;
  }
}

.hamburger-line {
  width: 100%;
  height: 2px;
  background: white;
  border-radius: var(--radius-full);
  transition: all 0.3s var(--ease-out);
}

.nav-toggle[aria-expanded="true"] .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.nav-toggle[aria-expanded="true"] .hamburger-line:nth-child(2) {
  opacity: 0;
}

.nav-toggle[aria-expanded="true"] .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* ===================================
   HERO SECTION
   =================================== */

.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-20) 0 var(--space-16);
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.hero-container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-12);
  align-items: center;
  z-index: 3;
}

@media (min-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr 1fr;
    padding: 0 var(--space-8);
  }
}

.hero-content {
  text-align: center;
}

@media (min-width: 1024px) {
  .hero-content {
    text-align: left;
  }
}

.hero-title {
  font-size: var(--text-4xl);
  font-weight: 900;
  line-height: 1.1;
  color: white;
  margin-bottom: var(--space-6);
  overflow: hidden;
}

@media (min-width: 768px) {
  .hero-title {
    font-size: var(--text-6xl);
  }
}

.title-line {
  display: block;
  overflow: hidden;
}

.title-word {
  display: inline-block;
  margin-right: var(--space-4);
  opacity: 0;
  transform: translateY(100%);
  animation: slideInUp 0.8s var(--ease-out) forwards;
}

.title-word:nth-child(1) { animation-delay: 0.1s; }
.title-word:nth-child(2) { animation-delay: 0.2s; }
.title-line:nth-child(2) .title-word:nth-child(1) { animation-delay: 0.3s; }
.title-line:nth-child(2) .title-word:nth-child(2) { animation-delay: 0.4s; }
.title-line:nth-child(3) .title-word { animation-delay: 0.5s; }

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-subtitle {
  font-size: var(--text-lg);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--space-8);
  max-width: 600px;
}

@media (min-width: 1024px) {
  .hero-subtitle {
    max-width: none;
  }
}

.typewriter-text {
  display: inline-block;
  border-right: 2px solid #6366f1;
  animation: typewriter 3s steps(100) 1s forwards, blink 1s infinite;
  white-space: nowrap;
  overflow: hidden;
  width: 0;
}

@keyframes typewriter {
  to {
    width: 100%;
  }
}

@keyframes blink {
  0%, 50% { border-color: #6366f1; }
  51%, 100% { border-color: transparent; }
}

.hero-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  align-items: center;
}

@media (min-width: 640px) {
  .hero-actions {
    flex-direction: row;
    justify-content: center;
  }
}

@media (min-width: 1024px) {
  .hero-actions {
    justify-content: flex-start;
  }
}

.cta-primary {
  position: relative;
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
  font-weight: 600;
}

.cta-secondary {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  font-size: var(--text-base);
  font-weight: 500;
  border-radius: var(--radius-full);
  transition: all 0.3s var(--ease-out);
}

.cta-secondary:hover {
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.btn-icon {
  font-size: var(--text-sm);
  transition: transform 0.3s var(--ease-out);
}

.cta-secondary:hover .btn-icon {
  transform: translateX(4px);
}

.hero-visual {
  position: relative;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-elements {
  position: relative;
  width: 100%;
  height: 100%;
}

.floating-element {
  position: absolute;
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: var(--radius-xl);
  opacity: 0.8;
  animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
  transform: rotate(45deg);
}

.floating-element:nth-child(2) {
  top: 60%;
  right: 20%;
  animation-delay: 2s;
  border-radius: 50%;
  width: 60px;
  height: 60px;
}

.floating-element:nth-child(3) {
  bottom: 20%;
  left: 50%;
  animation-delay: 4s;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  width: 70px;
  height: 70px;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(10px) rotate(240deg);
  }
}

.scroll-indicator {
  position: absolute;
  bottom: var(--space-8);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  color: rgba(255, 255, 255, 0.6);
  font-size: var(--text-sm);
  z-index: 3;
}

.scroll-line {
  width: 1px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, #6366f1, transparent);
  animation: scrollPulse 2s ease-in-out infinite;
}

@keyframes scrollPulse {
  0%, 100% {
    opacity: 0.4;
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.2);
  }
}

/* ===================================
   SECTION HEADERS
   =================================== */

.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.section-title {
  font-size: var(--text-3xl);
  font-weight: 800;
  color: white;
  margin-bottom: var(--space-4);
}

@media (min-width: 768px) {
  .section-title {
    font-size: var(--text-4xl);
  }
}

.title-accent {
  color: #6366f1;
}

.section-subtitle {
  font-size: var(--text-lg);
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin: 0 auto;
}

/* ===================================
   SERVICES SECTION
   =================================== */

.services {
  padding: var(--space-20) 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  position: relative;
}

.services::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.services .container {
  position: relative;
  z-index: 2;
}

.services-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
}

@media (min-width: 768px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.service-card {
  padding: var(--space-8);
  text-align: center;
  transition: all 0.3s var(--ease-out);
  cursor: pointer;
}

.service-card:hover {
  transform: translateY(-8px) rotateX(5deg);
}

.card-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--space-6);
  padding: var(--space-4);
  background: var(--gradient-primary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s var(--ease-out);
}

.card-icon svg {
  width: 32px;
  height: 32px;
}

.service-card:hover .card-icon {
  transform: scale(1.1) rotateY(180deg);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: white;
  margin-bottom: var(--space-4);
}

.card-description {
  font-size: var(--text-base);
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

.card-link {
  color: #6366f1;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s var(--ease-out);
  position: relative;
}

.card-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: #6366f1;
  transition: width 0.3s var(--ease-out);
}

.card-link:hover::after {
  width: 100%;
}

/* ===================================
   PORTFOLIO SECTION
   =================================== */

.portfolio {
  padding: var(--space-20) 0;
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
  position: relative;
}

.portfolio-filters {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-12);
  flex-wrap: wrap;
}

.filter-btn {
  padding: var(--space-3) var(--space-6);
  border: 2px solid rgba(255, 255, 255, 0.2);
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-full);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s var(--ease-out);
}

.filter-btn:hover,
.filter-btn.active {
  border-color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
  color: white;
  transform: translateY(-2px);
}

.portfolio-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
}

@media (min-width: 768px) {
  .portfolio-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .portfolio-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.portfolio-item {
  position: relative;
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: all 0.3s var(--ease-out);
  cursor: pointer;
}

.portfolio-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.portfolio-image {
  position: relative;
  aspect-ratio: 4/3;
  overflow: hidden;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: var(--gradient-secondary);
  transition: all 0.3s var(--ease-out);
}

.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: var(--space-6);
  opacity: 0;
  transition: all 0.3s var(--ease-out);
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: white;
  margin-bottom: var(--space-2);
}

.portfolio-description {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--space-4);
}

.portfolio-btn {
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-sm);
}

/* ===================================
   ABOUT SECTION
   =================================== */

.about {
  padding: var(--space-20) 0;
  background: linear-gradient(135deg, #475569 0%, #64748b 100%);
  position: relative;
}

.team-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
}

@media (min-width: 768px) {
  .team-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .team-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.team-member {
  perspective: 1000px;
  height: 300px;
}

.member-card {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
  cursor: pointer;
}

.team-member:hover .member-card {
  transform: rotateY(180deg);
}

.member-front,
.member-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.member-front {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
}

.member-back {
  background: var(--gradient-primary);
  transform: rotateY(180deg);
}

.member-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-bottom: var(--space-4);
}

.member-name {
  font-size: var(--text-lg);
  font-weight: 700;
  color: white;
  margin-bottom: var(--space-2);
}

.member-role {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--text-base);
}

.member-bio {
  color: white;
  line-height: 1.6;
  margin-bottom: var(--space-4);
}

.member-social {
  display: flex;
  gap: var(--space-3);
}

.member-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  text-decoration: none;
  font-size: var(--text-lg);
  transition: all 0.3s var(--ease-out);
}

.member-social a:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* ===================================
   TESTIMONIALS SECTION
   =================================== */

.testimonials {
  padding: var(--space-20) 0;
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  position: relative;
}

.testimonials-carousel {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.testimonial-track {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-xl);
}

.testimonial-slide {
  display: none;
  padding: var(--space-8);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  text-align: center;
}

.testimonial-slide.active {
  display: block;
  animation: fadeIn 0.5s var(--ease-out);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.testimonial-content p {
  font-size: var(--text-lg);
  color: white;
  line-height: 1.6;
  margin-bottom: var(--space-6);
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
}

.author-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.author-name {
  font-size: var(--text-base);
  font-weight: 600;
  color: white;
  font-style: normal;
}

.author-title {
  font-size: var(--text-sm);
  color: rgba(255, 255, 255, 0.7);
}

.carousel-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.carousel-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  background: transparent;
  color: white;
  border-radius: 50%;
  font-size: var(--text-xl);
  cursor: pointer;
  transition: all 0.3s var(--ease-out);
}

.carousel-btn:hover {
  border-color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
  transform: scale(1.1);
}

.carousel-dots {
  display: flex;
  gap: var(--space-2);
}

.dot {
  width: 12px;
  height: 12px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s var(--ease-out);
}

.dot.active,
.dot:hover {
  background: #6366f1;
  transform: scale(1.2);
}

/* ===================================
   CONTACT SECTION
   =================================== */

.contact {
  padding: var(--space-20) 0;
  background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
  position: relative;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-12);
  max-width: 1000px;
  margin: 0 auto;
}

@media (min-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr 1fr;
  }
}

.contact-info h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: white;
  margin-bottom: var(--space-4);
}

.contact-info p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: var(--space-8);
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  color: rgba(255, 255, 255, 0.9);
}

.contact-icon {
  font-size: var(--text-lg);
}

.contact-form {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
}

.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 600;
  color: white;
  margin-bottom: var(--space-2);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-4);
  border: 2px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  color: white;
  border-radius: var(--radius-lg);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  transition: all 0.3s var(--ease-out);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #6366f1;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-error {
  display: block;
  font-size: var(--text-sm);
  color: #ef4444;
  margin-top: var(--space-1);
  opacity: 0;
  transition: opacity 0.3s var(--ease-out);
}

.form-error.visible {
  opacity: 1;
}

.form-submit {
  width: 100%;
  position: relative;
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-base);
  font-weight: 600;
  overflow: hidden;
}

.btn-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  background: var(--gradient-primary);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s var(--ease-out);
}

.form-submit.loading .btn-text {
  opacity: 0;
}

.form-submit.loading .btn-loading {
  opacity: 1;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.form-success {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: var(--radius-lg);
  color: #22c55e;
  margin-top: var(--space-4);
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s var(--ease-out);
}

.form-success.visible {
  opacity: 1;
  transform: translateY(0);
}

.success-icon {
  font-size: var(--text-lg);
  font-weight: bold;
}

/* ===================================
   FOOTER
   =================================== */

.footer {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  padding: var(--space-16) 0 var(--space-8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

@media (min-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr 2fr;
  }
}

.footer-brand {
  text-align: center;
}

@media (min-width: 768px) {
  .footer-brand {
    text-align: left;
  }
}

.footer-logo {
  display: inline-flex;
  align-items: center;
  gap: var(--space-3);
  text-decoration: none;
  color: white;
  font-weight: 700;
  font-size: var(--text-lg);
  margin-bottom: var(--space-4);
  transition: all 0.3s var(--ease-out);
}

.footer-logo:hover {
  transform: scale(1.05);
}

.footer-tagline {
  color: rgba(255, 255, 255, 0.7);
  font-size: var(--text-base);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-8);
}

.footer-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: white;
  margin-bottom: var(--space-4);
}

.footer-list {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.footer-link {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s var(--ease-out);
}

.footer-link:hover {
  color: white;
  transform: translateX(4px);
}

.social-links {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-6);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s var(--ease-out);
}

.social-link:hover {
  background: var(--gradient-primary);
  color: white;
  transform: translateY(-2px);
}

.social-link svg {
  width: 20px;
  height: 20px;
}

.newsletter-title {
  font-size: var(--text-sm);
  font-weight: 600;
  color: white;
  margin-bottom: var(--space-3);
}

.newsletter-form {
  display: flex;
  gap: var(--space-2);
}

.newsletter-input {
  flex: 1;
  padding: var(--space-3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.05);
  color: white;
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  transition: all 0.3s var(--ease-out);
}

.newsletter-input:focus {
  outline: none;
  border-color: #6366f1;
  background: rgba(255, 255, 255, 0.1);
}

.newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.newsletter-btn {
  padding: var(--space-3);
  border: none;
  background: var(--gradient-primary);
  color: white;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.3s var(--ease-out);
  min-width: 48px;
}

.newsletter-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.footer-bottom {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
  padding-top: var(--space-8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

@media (min-width: 768px) {
  .footer-bottom {
    flex-direction: row;
  }
}

.footer-copyright {
  color: rgba(255, 255, 255, 0.6);
  font-size: var(--text-sm);
}

.footer-legal {
  display: flex;
  gap: var(--space-6);
}

.legal-link {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  font-size: var(--text-sm);
  transition: color 0.3s var(--ease-out);
}

.legal-link:hover {
  color: white;
}

/* ===================================
   RESPONSIVE DESIGN & MEDIA QUERIES
   =================================== */

@media (max-width: 767px) {
  .nav-menu {
    position: fixed;
    top: 100%;
    left: 0;
    width: 100%;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border-top: 1px solid var(--glass-border);
    flex-direction: column;
    padding: var(--space-6);
    gap: var(--space-4);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s var(--ease-out);
  }

  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .hero-title {
    font-size: var(--text-3xl);
  }

  .floating-elements {
    display: none;
  }
}

/* ===================================
   ACCESSIBILITY ENHANCEMENTS
   =================================== */

@media (prefers-reduced-motion: reduce) {
  .floating-element,
  .scroll-line,
  .loading-spinner {
    animation: none;
  }

  .typewriter-text {
    animation: none;
    width: 100%;
    border-right: none;
  }

  .title-word {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

/* Focus indicators */
.nav-link:focus,
.magnetic-btn:focus,
.filter-btn:focus,
.carousel-btn:focus,
.dot:focus,
.form-input:focus,
.form-select:focus,
.form-textarea:focus,
.social-link:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-card {
    border-width: 2px;
  }

  .nav-link::after,
  .card-link::after {
    height: 3px;
  }
}

/* ===================================
   ADVANCED ANIMATIONS & EFFECTS
   =================================== */

/* SVG Path Drawing Animation */
@keyframes drawPath {
  from {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dasharray: 1000;
    stroke-dashoffset: 0;
  }
}

.animate-draw {
  animation: drawPath 2s ease-in-out forwards;
}

/* Glitch Effect for Text */
@keyframes glitch {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(0);
  }
}

.glitch-effect {
  position: relative;
  animation: glitch 0.3s ease-in-out infinite;
}

.glitch-effect::before,
.glitch-effect::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch-effect::before {
  color: #ff0000;
  z-index: -1;
  animation: glitch 0.3s ease-in-out infinite reverse;
}

.glitch-effect::after {
  color: #00ffff;
  z-index: -2;
  animation: glitch 0.3s ease-in-out infinite;
}

/* Pulse Animation */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.pulse-animation {
  animation: pulse 2s ease-in-out infinite;
}

/* Floating Animation */
@keyframes floatUp {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.float-up {
  animation: floatUp 3s ease-out forwards;
}

/* Morphing Background */
@keyframes morphBackground {
  0% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  50% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
  100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
}

.morphing-bg {
  animation: morphBackground 8s ease-in-out infinite;
}

/* Particle Burst Effect */
@keyframes particleBurst {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(180deg);
    opacity: 0;
  }
}

.particle-burst {
  position: relative;
  overflow: visible;
}

.particle-burst::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: particleBurst 1s ease-out forwards;
}

/* Neon Glow Effect */
.neon-glow {
  text-shadow:
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px #6366f1,
    0 0 35px #6366f1,
    0 0 40px #6366f1;
  animation: neonFlicker 2s ease-in-out infinite alternate;
}

@keyframes neonFlicker {
  0%, 18%, 22%, 25%, 53%, 57%, 100% {
    text-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px #6366f1,
      0 0 35px #6366f1,
      0 0 40px #6366f1;
  }
  20%, 24%, 55% {
    text-shadow: none;
  }
}

/* Liquid Animation */
@keyframes liquid {
  0%, 100% {
    clip-path: polygon(0% 45%, 15% 44%, 32% 50%, 54% 60%, 70% 61%, 84% 59%, 100% 52%, 100% 100%, 0% 100%);
  }
  50% {
    clip-path: polygon(0% 60%, 16% 65%, 34% 66%, 51% 62%, 67% 50%, 84% 45%, 100% 46%, 100% 100%, 0% 100%);
  }
}

.liquid-effect {
  position: relative;
  overflow: hidden;
}

.liquid-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  animation: liquid 4s ease-in-out infinite;
  z-index: -1;
}

/* Matrix Rain Effect */
@keyframes matrixRain {
  0% {
    transform: translateY(-100vh);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

.matrix-rain {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.matrix-rain::before {
  content: '01010101010101010101010101010101010101010101';
  position: absolute;
  top: -100vh;
  left: 0;
  width: 100%;
  height: 200vh;
  color: rgba(99, 102, 241, 0.1);
  font-family: var(--font-mono);
  font-size: var(--text-xs);
  line-height: 1.2;
  word-break: break-all;
  animation: matrixRain 10s linear infinite;
}

/* Holographic Effect */
.holographic {
  position: relative;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  background-size: 200% 200%;
  animation: holographicShine 3s ease-in-out infinite;
}

@keyframes holographicShine {
  0% {
    background-position: -200% -200%;
  }
  100% {
    background-position: 200% 200%;
  }
}

/* Quantum Dots Animation */
@keyframes quantumDots {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: scale(1.2) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: scale(0.8) rotate(180deg);
    opacity: 0.5;
  }
  75% {
    transform: scale(1.1) rotate(270deg);
    opacity: 0.9;
  }
}

.quantum-dots {
  position: relative;
}

.quantum-dots::before,
.quantum-dots::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--gradient-primary);
  border-radius: 50%;
  animation: quantumDots 4s ease-in-out infinite;
}

.quantum-dots::before {
  top: -10px;
  left: -10px;
  animation-delay: 0s;
}

.quantum-dots::after {
  bottom: -10px;
  right: -10px;
  animation-delay: 2s;
}

/* Cyberpunk Grid */
.cyberpunk-grid {
  position: relative;
  background-image:
    linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 20px;
  }
}

/* Energy Orb Effect */
@keyframes energyOrb {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.8);
  }
}

.energy-orb {
  border-radius: 50%;
  animation: energyOrb 2s ease-in-out infinite;
}

/* Scan Line Effect */
@keyframes scanLine {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100vh);
  }
}

.scan-line {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #6366f1, transparent);
  animation: scanLine 3s ease-in-out infinite;
  z-index: 9999;
  pointer-events: none;
}

/* Animate on scroll classes */
.animate-in {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s var(--ease-out);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.6s var(--ease-out);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.6s var(--ease-out);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.6s var(--ease-out);
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.smooth-scroll {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
}
