<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Nexus Studios - Cutting-edge AI-powered design agency creating the future of digital experiences">
    <meta name="keywords" content="AI design, digital agency, web design, UX/UI, creative studio">
    <meta name="author" content="Nexus Studios">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://nexusstudios.com/">
    <meta property="og:title" content="Nexus Studios - AI-Powered Design Agency">
    <meta property="og:description" content="Where artificial intelligence meets creative brilliance">
    <meta property="og:image" content="https://nexusstudios.com/og-image.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://nexusstudios.com/">
    <meta property="twitter:title" content="Nexus Studios - AI-Powered Design Agency">
    <meta property="twitter:description" content="Where artificial intelligence meets creative brilliance">
    <meta property="twitter:image" content="https://nexusstudios.com/og-image.jpg">

    <title>Nexus Studios - Where AI Meets Creative Brilliance</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="styles.css" as="style">
    <link rel="preload" href="script.js" as="script">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="icon" type="image/png" href="favicon.png">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen" aria-hidden="true">
        <div class="loading-content">
            <div class="loading-logo">
                <svg class="logo-svg" viewBox="0 0 100 100" aria-label="Nexus Studios Logo">
                    <defs>
                        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#6366f1"/>
                            <stop offset="50%" style="stop-color:#8b5cf6"/>
                            <stop offset="100%" style="stop-color:#ec4899"/>
                        </linearGradient>
                    </defs>
                    <path class="logo-path" d="M20,20 L80,20 L80,80 L20,80 Z M30,30 L70,70 M70,30 L30,70" 
                          fill="none" stroke="url(#logoGradient)" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </div>
            <div class="loading-text">
                <span class="loading-letter">N</span>
                <span class="loading-letter">E</span>
                <span class="loading-letter">X</span>
                <span class="loading-letter">U</span>
                <span class="loading-letter">S</span>
            </div>
            <div class="loading-progress">
                <div class="progress-bar"></div>
            </div>
        </div>
    </div>

    <!-- Particle Canvas -->
    <canvas id="particle-canvas" class="particle-canvas" aria-hidden="true"></canvas>
    
    <!-- Constellation Background -->
    <div id="constellation" class="constellation" aria-hidden="true"></div>

    <!-- Navigation -->
    <nav class="nav" id="main-nav" role="navigation" aria-label="Main navigation">
        <div class="nav-container">
            <a href="#home" class="nav-logo" aria-label="Nexus Studios Home">
                <svg class="logo-icon" viewBox="0 0 40 40">
                    <path d="M8,8 L32,8 L32,32 L8,32 Z M12,12 L28,28 M28,12 L12,28" 
                          fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                <span class="logo-text">Nexus</span>
            </a>
            
            <ul class="nav-menu" role="menubar">
                <li role="none"><a href="#services" class="nav-link" role="menuitem">Services</a></li>
                <li role="none"><a href="#portfolio" class="nav-link" role="menuitem">Portfolio</a></li>
                <li role="none"><a href="#about" class="nav-link" role="menuitem">About</a></li>
                <li role="none"><a href="#contact" class="nav-link" role="menuitem">Contact</a></li>
            </ul>
            
            <div class="nav-actions">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode" type="button">
                    <span class="theme-icon theme-icon-light">☀️</span>
                    <span class="theme-icon theme-icon-dark">🌙</span>
                </button>
                <button class="nav-cta magnetic-btn" type="button">
                    <span class="btn-text">Start Project</span>
                    <span class="btn-bg"></span>
                </button>
            </div>
            
            <button class="nav-toggle" id="nav-toggle" aria-label="Toggle navigation menu" aria-expanded="false">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main" id="main-content">
        <!-- Hero Section -->
        <section class="hero" id="home" aria-labelledby="hero-title">
            <div class="hero-container">
                <div class="hero-content">
                    <h1 class="hero-title" id="hero-title">
                        <span class="title-line">
                            <span class="title-word" data-text="Where">Where</span>
                            <span class="title-word" data-text="AI">AI</span>
                        </span>
                        <span class="title-line">
                            <span class="title-word" data-text="Meets">Meets</span>
                            <span class="title-word gradient-text" data-text="Creative">Creative</span>
                        </span>
                        <span class="title-line">
                            <span class="title-word gradient-text" data-text="Brilliance">Brilliance</span>
                        </span>
                    </h1>
                    
                    <p class="hero-subtitle">
                        <span class="typewriter-text" data-text="We craft digital experiences that blur the line between imagination and reality, powered by cutting-edge AI and boundless creativity."></span>
                    </p>
                    
                    <div class="hero-actions">
                        <button class="cta-primary magnetic-btn" type="button">
                            <span class="btn-text">Explore Our Work</span>
                            <span class="btn-bg"></span>
                            <span class="btn-particles"></span>
                        </button>
                        <button class="cta-secondary magnetic-btn" type="button">
                            <span class="btn-text">Watch Demo</span>
                            <span class="btn-icon">▶</span>
                        </button>
                    </div>
                </div>
                
                <div class="hero-visual">
                    <div class="floating-elements">
                        <div class="floating-element" data-speed="0.5"></div>
                        <div class="floating-element" data-speed="0.8"></div>
                        <div class="floating-element" data-speed="0.3"></div>
                    </div>
                </div>
            </div>
            
            <div class="scroll-indicator">
                <div class="scroll-line"></div>
                <span class="scroll-text">Scroll to explore</span>
            </div>
        </section>

        <!-- Services Section -->
        <section class="services" id="services" aria-labelledby="services-title">
            <div class="container">
                <header class="section-header">
                    <h2 class="section-title" id="services-title">
                        <span class="title-accent">Our</span> Services
                    </h2>
                    <p class="section-subtitle">Transforming ideas into digital masterpieces</p>
                </header>
                
                <div class="services-grid">
                    <article class="service-card glass-card" data-tilt>
                        <div class="card-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M12 2L2 7v10c0 5.55 3.84 10 9 11 5.16-1 9-5.45 9-11V7l-10-5z"/>
                            </svg>
                        </div>
                        <h3 class="card-title">AI-Powered Design</h3>
                        <p class="card-description">Harness the power of artificial intelligence to create designs that adapt, learn, and evolve with your users.</p>
                        <a href="#" class="card-link">Learn More →</a>
                    </article>
                    
                    <article class="service-card glass-card" data-tilt>
                        <div class="card-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                            </svg>
                        </div>
                        <h3 class="card-title">3D Experiences</h3>
                        <p class="card-description">Immersive three-dimensional interfaces that transport users into new realms of digital interaction.</p>
                        <a href="#" class="card-link">Learn More →</a>
                    </article>
                    
                    <article class="service-card glass-card" data-tilt>
                        <div class="card-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                            </svg>
                        </div>
                        <h3 class="card-title">Performance Optimization</h3>
                        <p class="card-description">Lightning-fast experiences that load instantly and perform flawlessly across all devices and platforms.</p>
                        <a href="#" class="card-link">Learn More →</a>
                    </article>
                </div>
            </div>
        </section>

        <!-- Portfolio Section -->
        <section class="portfolio" id="portfolio" aria-labelledby="portfolio-title">
            <div class="container">
                <header class="section-header">
                    <h2 class="section-title" id="portfolio-title">
                        <span class="title-accent">Featured</span> Work
                    </h2>
                    <p class="section-subtitle">A showcase of our most innovative projects</p>
                </header>

                <div class="portfolio-filters">
                    <button class="filter-btn active" data-filter="all">All</button>
                    <button class="filter-btn" data-filter="web">Web Design</button>
                    <button class="filter-btn" data-filter="mobile">Mobile Apps</button>
                    <button class="filter-btn" data-filter="ai">AI Projects</button>
                </div>

                <div class="portfolio-grid">
                    <article class="portfolio-item" data-category="web ai">
                        <div class="portfolio-image">
                            <div class="image-placeholder gradient-bg"></div>
                            <div class="portfolio-overlay">
                                <h3 class="portfolio-title">Neural Commerce</h3>
                                <p class="portfolio-description">AI-powered e-commerce platform</p>
                                <button class="portfolio-btn magnetic-btn">View Project</button>
                            </div>
                        </div>
                    </article>

                    <article class="portfolio-item" data-category="mobile">
                        <div class="portfolio-image">
                            <div class="image-placeholder gradient-bg"></div>
                            <div class="portfolio-overlay">
                                <h3 class="portfolio-title">Mindful App</h3>
                                <p class="portfolio-description">Meditation and wellness mobile experience</p>
                                <button class="portfolio-btn magnetic-btn">View Project</button>
                            </div>
                        </div>
                    </article>

                    <article class="portfolio-item" data-category="web">
                        <div class="portfolio-image">
                            <div class="image-placeholder gradient-bg"></div>
                            <div class="portfolio-overlay">
                                <h3 class="portfolio-title">Quantum Dashboard</h3>
                                <p class="portfolio-description">Data visualization platform</p>
                                <button class="portfolio-btn magnetic-btn">View Project</button>
                            </div>
                        </div>
                    </article>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="about" id="about" aria-labelledby="about-title">
            <div class="container">
                <header class="section-header">
                    <h2 class="section-title" id="about-title">
                        <span class="title-accent">Meet</span> Our Team
                    </h2>
                    <p class="section-subtitle">The creative minds behind the magic</p>
                </header>

                <div class="team-grid">
                    <div class="team-member" data-tilt>
                        <div class="member-card">
                            <div class="member-front">
                                <div class="member-avatar gradient-bg"></div>
                                <h3 class="member-name">Alex Chen</h3>
                                <p class="member-role">Creative Director</p>
                            </div>
                            <div class="member-back">
                                <p class="member-bio">Visionary designer with 10+ years crafting digital experiences that push boundaries.</p>
                                <div class="member-social">
                                    <a href="#" aria-label="LinkedIn">💼</a>
                                    <a href="#" aria-label="Twitter">🐦</a>
                                    <a href="#" aria-label="Dribbble">🏀</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="team-member" data-tilt>
                        <div class="member-card">
                            <div class="member-front">
                                <div class="member-avatar gradient-bg"></div>
                                <h3 class="member-name">Sarah Kim</h3>
                                <p class="member-role">AI Engineer</p>
                            </div>
                            <div class="member-back">
                                <p class="member-bio">Machine learning expert specializing in creative AI applications and neural networks.</p>
                                <div class="member-social">
                                    <a href="#" aria-label="LinkedIn">💼</a>
                                    <a href="#" aria-label="GitHub">💻</a>
                                    <a href="#" aria-label="Medium">📝</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="team-member" data-tilt>
                        <div class="member-card">
                            <div class="member-front">
                                <div class="member-avatar gradient-bg"></div>
                                <h3 class="member-name">Marcus Rodriguez</h3>
                                <p class="member-role">Lead Developer</p>
                            </div>
                            <div class="member-back">
                                <p class="member-bio">Full-stack wizard who brings impossible designs to life with cutting-edge code.</p>
                                <div class="member-social">
                                    <a href="#" aria-label="LinkedIn">💼</a>
                                    <a href="#" aria-label="GitHub">💻</a>
                                    <a href="#" aria-label="Stack Overflow">📚</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section class="testimonials" id="testimonials" aria-labelledby="testimonials-title">
            <div class="container">
                <header class="section-header">
                    <h2 class="section-title" id="testimonials-title">
                        <span class="title-accent">Client</span> Love
                    </h2>
                    <p class="section-subtitle">What our partners say about working with us</p>
                </header>

                <div class="testimonials-carousel">
                    <div class="testimonial-track">
                        <div class="testimonial-slide active">
                            <blockquote class="testimonial-content">
                                <p>"Nexus Studios transformed our vision into a digital masterpiece that exceeded every expectation. Their AI-powered approach is truly revolutionary."</p>
                                <footer class="testimonial-author">
                                    <div class="author-avatar gradient-bg"></div>
                                    <div class="author-info">
                                        <cite class="author-name">Emily Watson</cite>
                                        <span class="author-title">CEO, TechFlow</span>
                                    </div>
                                </footer>
                            </blockquote>
                        </div>

                        <div class="testimonial-slide">
                            <blockquote class="testimonial-content">
                                <p>"The attention to detail and innovative solutions provided by Nexus Studios helped us achieve a 300% increase in user engagement."</p>
                                <footer class="testimonial-author">
                                    <div class="author-avatar gradient-bg"></div>
                                    <div class="author-info">
                                        <cite class="author-name">David Park</cite>
                                        <span class="author-title">Product Manager, InnovateCorp</span>
                                    </div>
                                </footer>
                            </blockquote>
                        </div>

                        <div class="testimonial-slide">
                            <blockquote class="testimonial-content">
                                <p>"Working with Nexus Studios was like having a crystal ball into the future of digital design. Absolutely phenomenal work."</p>
                                <footer class="testimonial-author">
                                    <div class="author-avatar gradient-bg"></div>
                                    <div class="author-info">
                                        <cite class="author-name">Lisa Chen</cite>
                                        <span class="author-title">Creative Director, BrandForward</span>
                                    </div>
                                </footer>
                            </blockquote>
                        </div>
                    </div>

                    <div class="carousel-controls">
                        <button class="carousel-btn prev" aria-label="Previous testimonial">‹</button>
                        <div class="carousel-dots">
                            <button class="dot active" data-slide="0" aria-label="Go to testimonial 1"></button>
                            <button class="dot" data-slide="1" aria-label="Go to testimonial 2"></button>
                            <button class="dot" data-slide="2" aria-label="Go to testimonial 3"></button>
                        </div>
                        <button class="carousel-btn next" aria-label="Next testimonial">›</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact" id="contact" aria-labelledby="contact-title">
            <div class="container">
                <header class="section-header">
                    <h2 class="section-title" id="contact-title">
                        <span class="title-accent">Let's</span> Create Together
                    </h2>
                    <p class="section-subtitle">Ready to bring your vision to life?</p>
                </header>

                <div class="contact-content">
                    <div class="contact-info">
                        <h3>Get in Touch</h3>
                        <p>We'd love to hear about your project and explore how we can help bring your ideas to life.</p>

                        <div class="contact-details">
                            <div class="contact-item">
                                <span class="contact-icon">📧</span>
                                <span><EMAIL></span>
                            </div>
                            <div class="contact-item">
                                <span class="contact-icon">📱</span>
                                <span>+1 (555) 123-4567</span>
                            </div>
                            <div class="contact-item">
                                <span class="contact-icon">📍</span>
                                <span>San Francisco, CA</span>
                            </div>
                        </div>
                    </div>

                    <form class="contact-form" id="contact-form" novalidate>
                        <div class="form-group">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" id="name" name="name" class="form-input" required>
                            <span class="form-error" id="name-error"></span>
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" id="email" name="email" class="form-input" required>
                            <span class="form-error" id="email-error"></span>
                        </div>

                        <div class="form-group">
                            <label for="project" class="form-label">Project Type</label>
                            <select id="project" name="project" class="form-select" required>
                                <option value="">Select a project type</option>
                                <option value="web">Web Design</option>
                                <option value="mobile">Mobile App</option>
                                <option value="ai">AI Integration</option>
                                <option value="other">Other</option>
                            </select>
                            <span class="form-error" id="project-error"></span>
                        </div>

                        <div class="form-group">
                            <label for="message" class="form-label">Message</label>
                            <textarea id="message" name="message" class="form-textarea" rows="5" required></textarea>
                            <span class="form-error" id="message-error"></span>
                        </div>

                        <button type="submit" class="form-submit magnetic-btn">
                            <span class="btn-text">Send Message</span>
                            <span class="btn-bg"></span>
                            <span class="btn-loading">
                                <span class="loading-spinner"></span>
                                Sending...
                            </span>
                        </button>

                        <div class="form-success" id="form-success">
                            <span class="success-icon">✓</span>
                            <span class="success-text">Message sent successfully! We'll get back to you soon.</span>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <a href="#home" class="footer-logo" aria-label="Nexus Studios Home">
                        <svg class="logo-icon" viewBox="0 0 40 40">
                            <path d="M8,8 L32,8 L32,32 L8,32 Z M12,12 L28,28 M28,12 L12,28"
                                  fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                        <span class="logo-text">Nexus Studios</span>
                    </a>
                    <p class="footer-tagline">Where AI meets creative brilliance</p>
                </div>

                <div class="footer-links">
                    <div class="footer-column">
                        <h4 class="footer-title">Services</h4>
                        <ul class="footer-list">
                            <li><a href="#" class="footer-link">AI-Powered Design</a></li>
                            <li><a href="#" class="footer-link">3D Experiences</a></li>
                            <li><a href="#" class="footer-link">Performance Optimization</a></li>
                            <li><a href="#" class="footer-link">Consulting</a></li>
                        </ul>
                    </div>

                    <div class="footer-column">
                        <h4 class="footer-title">Company</h4>
                        <ul class="footer-list">
                            <li><a href="#about" class="footer-link">About</a></li>
                            <li><a href="#portfolio" class="footer-link">Portfolio</a></li>
                            <li><a href="#" class="footer-link">Careers</a></li>
                            <li><a href="#contact" class="footer-link">Contact</a></li>
                        </ul>
                    </div>

                    <div class="footer-column">
                        <h4 class="footer-title">Connect</h4>
                        <div class="social-links">
                            <a href="#" class="social-link" aria-label="Twitter">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"/>
                                </svg>
                            </a>
                            <a href="#" class="social-link" aria-label="LinkedIn">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/>
                                    <rect x="2" y="9" width="4" height="12"/>
                                    <circle cx="4" cy="4" r="2"/>
                                </svg>
                            </a>
                            <a href="#" class="social-link" aria-label="Dribbble">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <circle cx="12" cy="12" r="10"/>
                                    <path d="M8.56 2.75c4.37 6.03 6.02 9.42 8.03 17.72m2.54-15.38c-3.72 4.35-8.94 5.66-16.88 5.85m19.5 1.9c-3.5-.93-6.63-.82-8.94 0-2.58.92-5.01 2.86-7.44 6.32"/>
                                </svg>
                            </a>
                            <a href="#" class="social-link" aria-label="GitHub">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"/>
                                </svg>
                            </a>
                        </div>

                        <div class="newsletter">
                            <h5 class="newsletter-title">Stay Updated</h5>
                            <form class="newsletter-form" id="newsletter-form">
                                <input type="email" class="newsletter-input" placeholder="Enter your email" required>
                                <button type="submit" class="newsletter-btn" aria-label="Subscribe to newsletter">→</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p class="footer-copyright">© 2024 Nexus Studios. All rights reserved.</p>
                <div class="footer-legal">
                    <a href="#" class="legal-link">Privacy Policy</a>
                    <a href="#" class="legal-link">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="script.js"></script>
</body>
</html>
